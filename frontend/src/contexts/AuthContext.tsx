'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { api } from '@/lib/api';
import { PloverRole, RoleManager } from '@/lib/roles';
import { piUtils, PiAuthR<PERSON>ult, PiUser } from '@/lib/pi';

interface User extends PiUser {
  id?: number;
  email?: string;
  isActive?: boolean;
  isVerified?: boolean;
  createdAt?: string;
  updatedAt?: string;
  pi_role?: string; // Role from Pi SDK
  plover_role?: PloverRole; // Our custom role system
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  signIn: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);



  const isAuthenticated = !!user;

  const refreshUser = React.useCallback(async () => {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        setUser(null);
        return;
      }

      const response = await api.auth.me();
      if (response.data.user) {
        const userData = response.data.user;
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
      } else {
        // No user data returned, clear session
        setUser(null);
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
      }
    } catch (error: any) {
      console.error('Error refreshing user:', error);
      // Only clear session if it's an auth error (401/403)
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        setUser(null);
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
      }
      // For other errors (network, etc.), keep the session but log the error
    }
  }, []);

  const checkAuthStatus = React.useCallback(async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      const savedUser = localStorage.getItem('user');

      if (token && savedUser) {
        try {
          const userData = JSON.parse(savedUser);
          setUser(userData);
          // Verify token with backend in background
          await refreshUser();
        } catch (parseError) {
          console.error('Error parsing saved user data:', parseError);
          // Clear corrupted data
          localStorage.removeItem('authToken');
          localStorage.removeItem('user');
          setUser(null);
        }
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      // Clear invalid auth data
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, [refreshUser]);

  // Initialize Pi SDK on mount
  useEffect(() => {
    piUtils.init();
    checkAuthStatus();
  }, [checkAuthStatus]);



  const signIn = async () => {
    try {
      setLoading(true);

      if (!piUtils.isAvailable()) {
        throw new Error(`Pi SDK is not available. Please download Pi Browser to use this app.

Pi is a new digital currency developed by Stanford PhDs, with over 55 million members worldwide. To claim your Pi, follow this link https://minepi.com/Pnguweneza and use my username (Pnguweneza) as your invitation code.

This is a way of supporting me as the developer. Thank you!`);
      }

      // Handle incomplete payments during authentication
      const handleIncompletePayment = async (payment: unknown) => {
        try {
          await api.payments.incomplete(payment);
        } catch (error) {
          console.error('Error handling incomplete payment:', error);
        }
      };

      // Authenticate with Pi Network
      const authResult: PiAuthResult = await piUtils.authenticate(handleIncompletePayment);

      // Send auth result to backend for verification
      const response = await api.auth.signin({ authResult });

      if (response.data.user) {
        const userData = response.data.user;

        // Assign Plover role if not already set
        if (!userData.plover_role) {
          // Map Pi role to Plover role or assign default
          userData.plover_role = userData.pi_role
            ? RoleManager.mapPiRoleToPloverRole(userData.pi_role)
            : PloverRole.MEMBER; // Default role for new users
        }

        setUser(userData);

        // Store auth data
        localStorage.setItem('authToken', authResult.accessToken);
        localStorage.setItem('user', JSON.stringify(userData));
      }
    } catch (error: unknown) {
      console.error('Sign in error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to sign in';
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      
      // Call backend signout endpoint
      await api.auth.signout();
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      // Clear local auth data regardless of backend response
      setUser(null);
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      setLoading(false);
    }
  };



  const value: AuthContextType = {
    user,
    loading,
    isAuthenticated,
    signIn,
    signOut,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
