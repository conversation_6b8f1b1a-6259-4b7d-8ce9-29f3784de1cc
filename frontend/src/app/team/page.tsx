'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import AuthProvider from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import { api } from '@/lib/api';
import {
  UsersIcon,
  MagnifyingGlassIcon,
  UserPlusIcon,
  StarIcon,
  ShieldCheckIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';

interface TeamMember {
  id: number;
  username: string;
  role: 'OWNER' | 'ADMIN' | 'MEMBER' | 'VIEWER';
  joined_at: string;
  project_count: number;
  task_count: number;
}

const TeamPage: React.FC = () => {
  const { isAuthenticated, user: _user } = useAuth();
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [inviteUsername, setInviteUsername] = useState('');

  useEffect(() => {
    if (isAuthenticated) {
      fetchTeamMembers();
    }
  }, [isAuthenticated]);

  const fetchTeamMembers = async () => {
    try {
      const response = await api.users.list();
      setMembers(response.data.users || []);
    } catch (error) {
      console.error('Failed to fetch team members:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInviteMember = async () => {
    if (!inviteUsername.trim()) return;

    try {
      // TODO: Replace with actual API call
      console.log('Inviting user:', inviteUsername);
      setInviteUsername('');
      setShowInviteForm(false);
      // Refresh members list
      fetchTeamMembers();
    } catch (error) {
      console.error('Failed to invite member:', error);
    }
  };

  const getRoleIcon = (role: TeamMember['role']) => {
    switch (role) {
      case 'OWNER':
        return <StarIcon className="h-4 w-4 text-yellow-500" />;
      case 'ADMIN':
        return <ShieldCheckIcon className="h-4 w-4 text-blue-500" />;
      case 'VIEWER':
        return <EyeIcon className="h-4 w-4 text-gray-500" />;
      default:
        return <UsersIcon className="h-4 w-4 text-green-500" />;
    }
  };

  const getRoleColor = (role: TeamMember['role']) => {
    switch (role) {
      case 'OWNER':
        return 'bg-yellow-100 text-yellow-800';
      case 'ADMIN':
        return 'bg-blue-100 text-blue-800';
      case 'MEMBER':
        return 'bg-green-100 text-green-800';
      case 'VIEWER':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredMembers = members.filter(member =>
    member.username.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-primary-500 mb-4">
            Please sign in to view your team
          </h1>
          <Link href="/">
            <Button variant="primary">Go to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-primary-500">Team</h1>
            <p className="text-gray-600 mt-2">
              Manage your team members and their permissions
            </p>
          </div>
          <Button 
            variant="primary"
            onClick={() => setShowInviteForm(true)}
          >
            <UserPlusIcon className="h-5 w-5 mr-2" />
            Invite Member
          </Button>
        </div>

        {/* Search */}
        <div className="mb-6">
          <Input
            type="text"
            placeholder="Search team members..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<MagnifyingGlassIcon />}
          />
        </div>

        {/* Invite Form */}
        {showInviteForm && (
          <Card className="mb-6">
            <h3 className="text-lg font-semibold text-primary-500 mb-4">
              Invite Team Member
            </h3>
            <div className="flex space-x-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Enter Pi Network username..."
                  value={inviteUsername}
                  onChange={(e) => setInviteUsername(e.target.value)}
                />
              </div>
              <Button variant="primary" onClick={handleInviteMember}>
                Send Invite
              </Button>
              <Button 
                variant="secondary" 
                onClick={() => {
                  setShowInviteForm(false);
                  setInviteUsername('');
                }}
              >
                Cancel
              </Button>
            </div>
          </Card>
        )}

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading team members...</p>
          </div>
        )}

        {/* Team Members */}
        {!loading && (
          <div className="space-y-4">
            {filteredMembers.map((member) => (
              <Card key={member.id}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-primary-600 font-semibold text-lg">
                        {member.username.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold text-primary-500">
                        {member.username}
                      </h3>
                      <p className="text-sm text-gray-500">
                        Joined {new Date(member.joined_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-6">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-primary-500">
                        {member.project_count}
                      </div>
                      <div className="text-xs text-gray-500">Projects</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-lg font-semibold text-primary-500">
                        {member.task_count}
                      </div>
                      <div className="text-xs text-gray-500">Tasks</div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {getRoleIcon(member.role)}
                      <span className={`px-3 py-1 text-sm rounded-full ${getRoleColor(member.role)}`}>
                        {member.role}
                      </span>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && filteredMembers.length === 0 && (
          <div className="text-center py-12">
            <UsersIcon className="h-24 w-24 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-600 mb-2">
              {searchQuery ? 'No members found' : 'No team members yet'}
            </h2>
            <p className="text-gray-500 mb-6">
              {searchQuery 
                ? 'Try adjusting your search terms'
                : 'Invite team members to start collaborating'
              }
            </p>
            {!searchQuery && (
              <Button 
                variant="primary"
                onClick={() => setShowInviteForm(true)}
              >
                <UserPlusIcon className="h-5 w-5 mr-2" />
                Invite First Member
              </Button>
            )}
          </div>
        )}
      </main>
    </div>
  );
};

export default function Team() {
  return (
    <AuthProvider>
      <TeamPage />
    </AuthProvider>
  );
}
