'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import AuthProvider from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  CheckCircleIcon,
  FolderIcon,
  ClipboardDocumentListIcon,
  UsersIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';

interface GuideStep {
  id: number;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  content: React.ReactNode;
  actionText?: string;
  actionHref?: string;
}

const GuideSteps: GuideStep[] = [
  {
    id: 1,
    title: 'Welcome to Plover',
    description: 'Your Pi Network-powered project management tool',
    icon: CheckCircleIcon,
    content: (
      <div className="text-center">
        <div className="w-24 h-24 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircleIcon className="h-12 w-12 text-primary-500" />
        </div>
        <h3 className="text-2xl font-bold text-primary-500 mb-4">
          Welcome to Plover!
        </h3>
        <p className="text-gray-600 mb-6">
          Plover is a powerful project management tool built on the Pi Network.
          Let&apos;s take a quick tour to get you started with organizing your projects,
          boards, and tasks efficiently.
        </p>
        <div className="bg-primary-50 rounded-lg p-4">
          <p className="text-sm text-primary-700">
            💡 This guide will take about 3 minutes to complete
          </p>
        </div>
      </div>
    ),
  },
  {
    id: 2,
    title: 'Create Your First Project',
    description: 'Projects help you organize your work into manageable groups',
    icon: FolderIcon,
    content: (
      <div>
        <div className="flex items-center mb-4">
          <FolderIcon className="h-8 w-8 text-primary-500 mr-3" />
          <h3 className="text-xl font-semibold text-primary-500">Projects</h3>
        </div>
        <p className="text-gray-600 mb-6">
          Projects are the top-level containers for your work. Think of them as
          different initiatives, clients, or major goals you&apos;re working towards.
        </p>
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h4 className="font-semibold text-gray-900 mb-2">Examples:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Website Redesign</li>
            <li>• Mobile App Development</li>
            <li>• Marketing Campaign</li>
            <li>• Product Launch</li>
          </ul>
        </div>
        <p className="text-sm text-gray-500">
          Each project can contain multiple boards to organize different aspects of your work.
        </p>
      </div>
    ),
    actionText: 'Create Project',
    actionHref: '/projects',
  },
  {
    id: 3,
    title: 'Organize with Boards',
    description: 'Boards help you visualize and track progress using Kanban methodology',
    icon: ClipboardDocumentListIcon,
    content: (
      <div>
        <div className="flex items-center mb-4">
          <ClipboardDocumentListIcon className="h-8 w-8 text-primary-500 mr-3" />
          <h3 className="text-xl font-semibold text-primary-500">Boards</h3>
        </div>
        <p className="text-gray-600 mb-6">
          Boards use the Kanban methodology to help you visualize your workflow.
          Tasks move through columns like &quot;To Do&quot;, &quot;In Progress&quot;, and &quot;Completed&quot;.
        </p>
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="bg-gray-100 rounded-lg p-3 text-center">
            <h5 className="font-semibold text-gray-700 mb-2">To Do</h5>
            <div className="space-y-2">
              <div className="bg-white rounded p-2 text-xs">Task 1</div>
              <div className="bg-white rounded p-2 text-xs">Task 2</div>
            </div>
          </div>
          <div className="bg-yellow-100 rounded-lg p-3 text-center">
            <h5 className="font-semibold text-yellow-700 mb-2">In Progress</h5>
            <div className="space-y-2">
              <div className="bg-white rounded p-2 text-xs">Task 3</div>
            </div>
          </div>
          <div className="bg-green-100 rounded-lg p-3 text-center">
            <h5 className="font-semibold text-green-700 mb-2">Completed</h5>
            <div className="space-y-2">
              <div className="bg-white rounded p-2 text-xs">Task 4</div>
            </div>
          </div>
        </div>
        <p className="text-sm text-gray-500">
          You can create multiple boards within each project for different workflows.
        </p>
      </div>
    ),
    actionText: 'View Boards',
    actionHref: '/boards',
  },
  {
    id: 4,
    title: 'Manage Tasks',
    description: 'Break down your work into actionable tasks with priorities and due dates',
    icon: CheckCircleIcon,
    content: (
      <div>
        <div className="flex items-center mb-4">
          <CheckCircleIcon className="h-8 w-8 text-primary-500 mr-3" />
          <h3 className="text-xl font-semibold text-primary-500">Tasks</h3>
        </div>
        <p className="text-gray-600 mb-6">
          Tasks are the individual work items that make up your projects. 
          Each task can have descriptions, priorities, due dates, and assignees.
        </p>
        <div className="bg-white border rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between mb-2">
            <h5 className="font-semibold text-gray-900">Example Task</h5>
            <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">High</span>
          </div>
          <p className="text-sm text-gray-600 mb-3">
            Design the user interface for the login page with responsive layout
          </p>
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Due: Feb 15, 2024</span>
            <span>Assigned to: John Doe</span>
          </div>
        </div>
        <p className="text-sm text-gray-500">
          Tasks can be easily moved between board columns as work progresses.
        </p>
      </div>
    ),
    actionText: 'View Tasks',
    actionHref: '/tasks',
  },
  {
    id: 5,
    title: 'Collaborate with Your Team',
    description: 'Invite team members and manage permissions',
    icon: UsersIcon,
    content: (
      <div>
        <div className="flex items-center mb-4">
          <UsersIcon className="h-8 w-8 text-primary-500 mr-3" />
          <h3 className="text-xl font-semibold text-primary-500">Team Collaboration</h3>
        </div>
        <p className="text-gray-600 mb-6">
          Plover makes it easy to collaborate with your team. Invite members,
          assign tasks, and track everyone&apos;s progress in real-time.
        </p>
        <div className="space-y-4 mb-6">
          <div className="flex items-center p-3 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
              <span className="text-primary-600 text-sm font-medium">JD</span>
            </div>
            <div className="flex-1">
              <p className="font-medium text-gray-900">John Doe</p>
              <p className="text-sm text-gray-500">Project Manager</p>
            </div>
            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Admin</span>
          </div>
          <div className="flex items-center p-3 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
              <span className="text-primary-600 text-sm font-medium">JS</span>
            </div>
            <div className="flex-1">
              <p className="font-medium text-gray-900">Jane Smith</p>
              <p className="text-sm text-gray-500">Developer</p>
            </div>
            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Member</span>
          </div>
        </div>
        <p className="text-sm text-gray-500">
          Team members can be assigned different roles with appropriate permissions.
        </p>
      </div>
    ),
    actionText: 'Manage Team',
    actionHref: '/team',
  },
  {
    id: 6,
    title: 'Customize Your Experience',
    description: 'Personalize settings and manage your subscription',
    icon: Cog6ToothIcon,
    content: (
      <div>
        <div className="flex items-center mb-4">
          <Cog6ToothIcon className="h-8 w-8 text-primary-500 mr-3" />
          <h3 className="text-xl font-semibold text-primary-500">Settings & More</h3>
        </div>
        <p className="text-gray-600 mb-6">
          Customize Plover to work the way you do. Adjust settings, manage your 
          subscription, and get help when you need it.
        </p>
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-gray-50 rounded-lg p-4 text-center">
            <Cog6ToothIcon className="h-6 w-6 text-gray-600 mx-auto mb-2" />
            <h5 className="font-semibold text-gray-700 text-sm">Settings</h5>
            <p className="text-xs text-gray-500 mt-1">Profile & preferences</p>
          </div>
          <div className="bg-gray-50 rounded-lg p-4 text-center">
            <CheckCircleIcon className="h-6 w-6 text-gray-600 mx-auto mb-2" />
            <h5 className="font-semibold text-gray-700 text-sm">Subscription</h5>
            <p className="text-xs text-gray-500 mt-1">Plans & billing</p>
          </div>
        </div>
        <div className="bg-primary-50 rounded-lg p-4">
          <p className="text-sm text-primary-700 text-center">
            🎉 You&apos;re all set! Start creating your first project to begin organizing your work.
          </p>
        </div>
      </div>
    ),
    actionText: 'Go to Settings',
    actionHref: '/settings',
  },
];

const GuidePage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completed, setCompleted] = useState(false);

  const handleNext = () => {
    if (currentStep < GuideSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setCompleted(true);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    setCompleted(true);
  };

  if (completed) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card>
            <div className="text-center py-12">
              <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-6" />
              <h1 className="text-3xl font-bold text-primary-500 mb-4">
                Welcome to Plover!
              </h1>
              <p className="text-gray-600 mb-8">
                You&apos;re all set to start managing your projects efficiently.
                Create your first project to get started.
              </p>
              <div className="flex justify-center space-x-4">
                <Link href="/projects">
                  <Button variant="primary">
                    Create First Project
                  </Button>
                </Link>
                <Link href="/">
                  <Button variant="secondary">
                    Go to Dashboard
                  </Button>
                </Link>
              </div>
            </div>
          </Card>
        </main>
      </div>
    );
  }

  const step = GuideSteps[currentStep];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-2xl font-bold text-primary-500">Getting Started Guide</h1>
            <button
              onClick={handleSkip}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Skip Guide
            </button>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-primary-500 h-2 rounded-full transition-all duration-300" 
              style={{ width: `${((currentStep + 1) / GuideSteps.length) * 100}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Step {currentStep + 1} of {GuideSteps.length}
          </p>
        </div>

        {/* Step Content */}
        <Card>
          <div className="min-h-[500px]">
            <div className="mb-6">
              <div className="flex items-center mb-4">
                <step.icon className="h-8 w-8 text-primary-500 mr-3" />
                <div>
                  <h2 className="text-2xl font-bold text-primary-500">{step.title}</h2>
                  <p className="text-gray-600">{step.description}</p>
                </div>
              </div>
            </div>

            <div className="mb-8">
              {step.content}
            </div>

            {/* Navigation */}
            <div className="flex items-center justify-between pt-6 border-t border-gray-200">
              <Button
                variant="secondary"
                onClick={handlePrevious}
                disabled={currentStep === 0}
              >
                <ChevronLeftIcon className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex space-x-3">
                {step.actionText && step.actionHref && (
                  <Link href={step.actionHref}>
                    <Button variant="secondary" size="sm">
                      {step.actionText}
                    </Button>
                  </Link>
                )}
                
                <Button variant="primary" onClick={handleNext}>
                  {currentStep === GuideSteps.length - 1 ? 'Finish' : 'Next'}
                  {currentStep < GuideSteps.length - 1 && (
                    <ChevronRightIcon className="h-4 w-4 ml-2" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </main>
    </div>
  );
};

export default function Guide() {
  return (
    <AuthProvider>
      <GuidePage />
    </AuthProvider>
  );
}
