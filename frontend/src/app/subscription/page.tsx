'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import AuthProvider from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import {
  CreditCardIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  StarIcon,
} from '@heroicons/react/24/outline';

interface SubscriptionData {
  plan: 'FREE' | 'BASIC' | 'PRO' | 'ENTERPRISE';
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'TRIAL';
  expires_at: string | null;
  trial_ends_at: string | null;
  days_remaining: number;
  features: {
    projects: number;
    boards: number;
    tasks: number;
    team_members: number;
    storage_gb: number;
  };
  usage: {
    projects: number;
    boards: number;
    tasks: number;
    team_members: number;
    storage_used_gb: number;
  };
}

const SubscriptionPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [subscription, setSubscription] = useState<SubscriptionData | null>(null);

  useEffect(() => {
    if (isAuthenticated) {
      fetchSubscription();
    }
  }, [isAuthenticated]);

  const fetchSubscription = async () => {
    try {
      // TODO: Replace with actual API call
      // Mock data for now
      const mockSubscription: SubscriptionData = {
        plan: 'FREE',
        status: 'ACTIVE',
        expires_at: null,
        trial_ends_at: '2024-03-01T00:00:00Z',
        days_remaining: 15,
        features: {
          projects: 3,
          boards: 10,
          tasks: 100,
          team_members: 5,
          storage_gb: 1,
        },
        usage: {
          projects: 2,
          boards: 5,
          tasks: 23,
          team_members: 3,
          storage_used_gb: 0.2,
        },
      };
      
      setSubscription(mockSubscription);
    } catch (error) {
      console.error('Failed to fetch subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'FREE':
        return 'text-gray-600 bg-gray-100';
      case 'BASIC':
        return 'text-blue-600 bg-blue-100';
      case 'PRO':
        return 'text-purple-600 bg-purple-100';
      case 'ENTERPRISE':
        return 'text-gold-600 bg-gold-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'EXPIRED':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'TRIAL':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getUsagePercentage = (used: number, limit: number) => {
    return Math.min((used / limit) * 100, 100);
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-primary-500 mb-4">
            Please sign in to view your subscription
          </h1>
          <Link href="/">
            <Button variant="primary">Go to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading subscription...</p>
          </div>
        </main>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-600 mb-4">No subscription found</h2>
            <Button variant="primary">Contact Support</Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-primary-500">Subscription</h1>
          <p className="text-gray-600 mt-2">
            Manage your subscription and billing information
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Current Plan */}
          <div className="lg:col-span-2">
            <Card>
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <CreditCardIcon className="h-6 w-6 text-primary-500 mr-3" />
                  <h2 className="text-xl font-semibold text-primary-500">Current Plan</h2>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(subscription.status)}
                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${getPlanColor(subscription.plan)}`}>
                    {subscription.plan}
                  </span>
                </div>
              </div>

              {/* Trial/Expiry Warning */}
              {subscription.status === 'TRIAL' && subscription.days_remaining <= 30 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center">
                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-yellow-800">
                        Trial expires in {subscription.days_remaining} days
                      </p>
                      <p className="text-sm text-yellow-700 mt-1">
                        Upgrade now to continue using all features
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Usage Stats */}
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900">Usage</h3>
                
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Projects</span>
                      <span>{subscription.usage.projects} / {subscription.features.projects}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full" 
                        style={{ width: `${getUsagePercentage(subscription.usage.projects, subscription.features.projects)}%` }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Boards</span>
                      <span>{subscription.usage.boards} / {subscription.features.boards}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full" 
                        style={{ width: `${getUsagePercentage(subscription.usage.boards, subscription.features.boards)}%` }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Tasks</span>
                      <span>{subscription.usage.tasks} / {subscription.features.tasks}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full" 
                        style={{ width: `${getUsagePercentage(subscription.usage.tasks, subscription.features.tasks)}%` }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Team Members</span>
                      <span>{subscription.usage.team_members} / {subscription.features.team_members}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full" 
                        style={{ width: `${getUsagePercentage(subscription.usage.team_members, subscription.features.team_members)}%` }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Storage</span>
                      <span>{subscription.usage.storage_used_gb} GB / {subscription.features.storage_gb} GB</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full" 
                        style={{ width: `${getUsagePercentage(subscription.usage.storage_used_gb, subscription.features.storage_gb)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Actions */}
          <div className="space-y-6">
            {/* Upgrade */}
            <Card>
              <div className="text-center">
                <StarIcon className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Upgrade Your Plan
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Get more features and higher limits
                </p>
                <Button variant="primary" className="w-full">
                  View Plans
                </Button>
              </div>
            </Card>

            {/* Billing */}
            <Card>
              <h3 className="font-semibold text-gray-900 mb-4">Billing</h3>
              <div className="space-y-3">
                <Button variant="secondary" size="sm" className="w-full">
                  Payment Methods
                </Button>
                <Button variant="secondary" size="sm" className="w-full">
                  Billing History
                </Button>
                <Button variant="secondary" size="sm" className="w-full">
                  Download Invoice
                </Button>
              </div>
            </Card>

            {/* Support */}
            <Card>
              <h3 className="font-semibold text-gray-900 mb-4">Need Help?</h3>
              <div className="space-y-3">
                <Link href="/support">
                  <Button variant="secondary" size="sm" className="w-full">
                    Contact Support
                  </Button>
                </Link>
                <Link href="/guide">
                  <Button variant="secondary" size="sm" className="w-full">
                    User Guide
                  </Button>
                </Link>
              </div>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default function Subscription() {
  return (
    <AuthProvider>
      <SubscriptionPage />
    </AuthProvider>
  );
}
