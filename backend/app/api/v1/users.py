"""
User management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.database.connection import get_db
from app.schemas.user import UserResponse, UserProfile
from app.core.dependencies import get_current_active_user, require_admin
from app.models.user import User
from app.database.utils import DatabaseUtils

router = APIRouter()


@router.get("/profile", response_model=UserProfile)
async def get_user_profile(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user profile"""
    return UserProfile.model_validate(current_user)


@router.get("/", response_model=List[UserProfile])
async def list_users(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin())
):
    """List all users (admin only)"""
    users = await DatabaseUtils.get_all(db, User, skip=skip, limit=limit)
    return [UserProfile.model_validate(user) for user in users]


@router.get("/{user_id}", response_model=UserProfile)
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get user by ID"""
    user = await DatabaseUtils.get_by_id(db, User, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return UserProfile.model_validate(user)
