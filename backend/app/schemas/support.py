"""
Support ticket schemas
"""

from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
from enum import Enum


class TicketStatus(str, Enum):
    OPEN = "OPEN"
    IN_PROGRESS = "IN_PROGRESS"
    RESOLVED = "RESOLVED"
    CLOSED = "CLOSED"


class TicketPriority(str, Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"


class TicketCategory(str, Enum):
    GENERAL = "general"
    TECHNICAL = "technical"
    BILLING = "billing"
    FEATURE_REQUEST = "feature_request"
    BUG_REPORT = "bug_report"


class SupportTicketBase(BaseModel):
    """Base support ticket schema"""
    subject: str = Field(..., min_length=1, max_length=255)
    message: str = Field(..., min_length=1)
    category: TicketCategory = TicketCategory.GENERAL
    priority: Optional[TicketPriority] = TicketPriority.MEDIUM
    status: Optional[TicketStatus] = TicketStatus.OPEN


class SupportTicketCreate(SupportTicketBase):
    """Schema for creating a support ticket"""
    ticket_number: str = Field(..., min_length=1, max_length=50)


class SupportTicketUpdate(BaseModel):
    """Schema for updating a support ticket"""
    subject: Optional[str] = Field(None, min_length=1, max_length=255)
    message: Optional[str] = Field(None, min_length=1)
    category: Optional[TicketCategory] = None
    priority: Optional[TicketPriority] = None
    status: Optional[TicketStatus] = None


class SupportTicketResponse(SupportTicketBase):
    """Schema for support ticket response"""
    id: str
    user_id: str
    ticket_number: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
